"""
特征工程模块
实现文本特征提取、数值特征处理、特征标准化等功能
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import re
import string

class FeatureEngineer:
    def __init__(self, max_features=5000, test_size=0.2, random_state=42):
        """
        初始化特征工程器
        
        Args:
            max_features: TF-IDF最大特征数量
            test_size: 测试集比例
            random_state: 随机种子
        """
        self.max_features = max_features
        self.test_size = test_size
        self.random_state = random_state
        
        # 初始化组件
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=max_features,
            stop_words='english',
            lowercase=True,
            ngram_range=(1, 2)  # 使用1-gram和2-gram
        )
        self.scaler = StandardScaler()
        
    def clean_text(self, text):
        """
        清理文本数据
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        if pd.isna(text):
            return ""
        
        # 转换为小写
        text = str(text).lower()
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 移除特殊字符，保留字母、数字和空格
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def extract_text_features(self, texts):
        """
        提取文本统计特征
        
        Args:
            texts: 文本列表
            
        Returns:
            文本统计特征DataFrame
        """
        features = []
        
        for text in texts:
            if pd.isna(text):
                text = ""
            else:
                text = str(text)
            
            # 文本长度
            text_length = len(text)
            
            # 单词数量
            word_count = len(text.split())
            
            # 句子数量（以句号、感叹号、问号为分隔）
            sentence_count = len(re.findall(r'[.!?]+', text))
            
            # 平均单词长度
            words = text.split()
            avg_word_length = np.mean([len(word) for word in words]) if words else 0
            
            # 大写字母比例
            upper_ratio = sum(1 for c in text if c.isupper()) / len(text) if text else 0
            
            features.append([
                text_length, word_count, sentence_count, 
                avg_word_length, upper_ratio
            ])
        
        feature_names = [
            'text_length', 'word_count', 'sentence_count',
            'avg_word_length', 'upper_ratio'
        ]
        
        return pd.DataFrame(features, columns=feature_names)
    
    def process_features(self, df, is_training=True):
        """
        处理所有特征
        
        Args:
            df: 数据DataFrame
            is_training: 是否为训练数据
            
        Returns:
            处理后的特征矩阵
        """
        # 1. 清理文本
        clean_texts = [self.clean_text(text) for text in df['reviewText']]
        
        # 2. TF-IDF特征
        if is_training:
            tfidf_features = self.tfidf_vectorizer.fit_transform(clean_texts)
        else:
            tfidf_features = self.tfidf_vectorizer.transform(clean_texts)
        
        # 转换为DataFrame
        tfidf_df = pd.DataFrame(
            tfidf_features.toarray(),
            columns=[f'tfidf_{i}' for i in range(tfidf_features.shape[1])]
        )
        
        # 3. 文本统计特征
        text_stats = self.extract_text_features(df['reviewText'])
        
        # 4. 数值特征
        numerical_features = pd.DataFrame()
        numerical_features['overall'] = df['overall']
        
        # 投票有用率（只在训练集中存在）
        if 'votes_up' in df.columns and 'votes_all' in df.columns:
            # 避免除零错误
            votes_all_safe = df['votes_all'].replace(0, 1)
            numerical_features['vote_ratio'] = df['votes_up'] / votes_all_safe
        else:
            # 测试集中没有投票信息，设为默认值
            numerical_features['vote_ratio'] = 0.5
        
        # 5. 标准化数值特征
        numerical_cols = ['overall', 'vote_ratio']
        text_stats_cols = text_stats.columns.tolist()
        all_numerical_cols = numerical_cols + text_stats_cols
        
        # 合并数值特征
        all_numerical = pd.concat([numerical_features, text_stats], axis=1)
        
        if is_training:
            scaled_numerical = self.scaler.fit_transform(all_numerical)
        else:
            scaled_numerical = self.scaler.transform(all_numerical)
        
        scaled_numerical_df = pd.DataFrame(
            scaled_numerical,
            columns=all_numerical_cols
        )
        
        # 6. 合并所有特征
        final_features = pd.concat([tfidf_df, scaled_numerical_df], axis=1)
        
        return final_features.values
    
    def prepare_data(self, train_df, test_df=None):
        """
        准备训练和测试数据
        
        Args:
            train_df: 训练数据
            test_df: 测试数据（可选）
            
        Returns:
            处理后的特征和标签
        """
        print("开始特征工程...")
        
        # 处理训练数据
        X_train_full = self.process_features(train_df, is_training=True)
        y_train_full = train_df['label'].values
        
        # 划分训练集和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X_train_full, y_train_full,
            test_size=self.test_size,
            random_state=self.random_state,
            stratify=y_train_full
        )
        
        print(f"训练集特征形状: {X_train.shape}")
        print(f"验证集特征形状: {X_val.shape}")
        print(f"特征总数: {X_train.shape[1]}")
        
        result = {
            'X_train': X_train,
            'X_val': X_val,
            'y_train': y_train,
            'y_val': y_val,
            'X_train_full': X_train_full,
            'y_train_full': y_train_full
        }
        
        # 如果提供了测试数据，也进行处理
        if test_df is not None:
            X_test = self.process_features(test_df, is_training=False)
            result['X_test'] = X_test
            print(f"测试集特征形状: {X_test.shape}")
        
        print("特征工程完成！")
        return result

if __name__ == "__main__":
    # 测试特征工程
    print("加载数据...")
    train_df = pd.read_csv('./data/train.csv', sep='\t')
    test_df = pd.read_csv('./data/test.csv', sep='\t')
    
    print(f"训练集形状: {train_df.shape}")
    print(f"测试集形状: {test_df.shape}")
    
    # 查看标签分布
    print(f"标签分布:\n{train_df['label'].value_counts()}")
    
    # 初始化特征工程器
    fe = FeatureEngineer(max_features=3000)
    
    # 处理数据
    data = fe.prepare_data(train_df, test_df)
    
    print("\n特征工程测试完成！")
