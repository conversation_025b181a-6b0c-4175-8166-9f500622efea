# 基于集成学习的Amazon用户评论质量预测实验任务规划

## 实验目标
实现两种集成学习算法（Bagging、AdaBoost.M1），使用SVM和决策树作为基分类器，对Amazon用户评论质量进行预测。

## 数据概览
- 训练集：57,039条评论数据
- 测试集：11,209条评论数据
- 特征：reviewerID, asin, reviewText, overall, votes_up, votes_all
- 目标：label (1=高质量, 0=低质量)
- 评价指标：AUC

## 核心任务分解

### 1. 数据预处理和特征工程
- [ ] 文本特征提取（TF-IDF、词袋模型等）
- [ ] 数值特征处理（overall评分、投票比例等）
- [ ] 特征标准化和归一化
- [ ] 数据集划分（训练集、验证集）

### 2. 基分类器实现
- [ ] SVM分类器（使用sklearn）
- [ ] 决策树分类器（使用sklearn）
- [ ] 基分类器性能测试

### 3. 集成学习算法手动实现
- [ ] Bagging算法实现
  - 自助采样（Bootstrap Sampling）
  - 多个基分类器训练
  - 投票机制
- [ ] AdaBoost.M1算法实现
  - 样本权重初始化
  - 迭代训练基分类器
  - 分类器权重计算
  - 样本权重更新

### 4. 模型组合和评估
- [ ] Bagging + SVM
- [ ] Bagging + 决策树
- [ ] AdaBoost.M1 + SVM
- [ ] AdaBoost.M1 + 决策树
- [ ] AUC计算和比较

### 5. 结果分析
- [ ] 不同组合的性能对比
- [ ] 集成学习算法特点分析
- [ ] 结果差异原因分析

### 6. 实验报告完善
- [ ] 实验步骤详细说明
- [ ] 代码实现展示
- [ ] 结果可视化
- [ ] 总结和讨论

## 技术实现要点

### 特征工程策略
1. **文本特征**：
   - TF-IDF向量化（限制特征数量避免维度过高）
   - 文本长度、单词数量等统计特征
   
2. **数值特征**：
   - overall评分
   - 投票有用率（votes_up/votes_all）
   - 用户ID和商品ID的编码

### 集成学习实现细节
1. **Bagging**：
   - 自助采样比例：100%
   - 基分类器数量：10-50个
   - 投票方式：软投票（概率平均）

2. **AdaBoost.M1**：
   - 最大迭代次数：50
   - 学习率控制
   - 错误率阈值设置

### 评估策略
- 使用交叉验证评估模型性能
- AUC作为主要评价指标
- 绘制ROC曲线进行可视化分析

## 预期挑战和解决方案

1. **文本特征维度过高**
   - 解决：限制TF-IDF特征数量，使用特征选择

2. **类别不平衡问题**
   - 解决：在集成学习中考虑样本权重

3. **计算效率问题**
   - 解决：合理设置基分类器数量，优化代码实现

4. **过拟合风险**
   - 解决：使用交叉验证，调整集成参数

## 文件组织结构
```
├── exp6.ipynb                 # 主实验报告
├── ensemble_learning.py       # 集成学习算法实现
├── feature_engineering.py     # 特征工程模块
├── model_evaluation.py        # 模型评估模块
├── data/                      # 数据目录
└── aidocs/                    # 文档目录
```
