{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exp6: 基于集成学习的 Amazon 用户评论质量预测"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 一、案例简介\n", "\n", "随着电商平台的兴起，以及疫情的持续影响，线上购物在我们的日常生活中扮演着越来越重要的角色。在进行线上商品挑选时，评论往往是我们十分关注的一个方面。然而目前电商网站的评论质量参差不齐，甚至有水军刷好评或者恶意差评的情况出现，严重影响了顾客的购物体验。因此，对于评论质量的预测成为电商平台越来越关注的话题，如果能自动对评论质量进行评估，就能根据预测结果避免展现低质量的评论。本案例中我们将基于集成学习的方法对 Amazon 现实场景中的评论质量进行预测。\n", "\n", "## 二、作业说明\n", "\n", "本案例中需要大家完成两种集成学习算法的实现（Bagging、AdaBoost.M1），其中基分类器要求使用 SVM 和决策树两种，因此，一共需要对比四组结果（[AUC](https://scikit-learn.org/stable/modules/model_evaluation.html#roc-metrics) 作为评价指标）：\n", "\n", "* Bagging + SVM\n", "* Bagging + 决策树\n", "* AdaBoost.M1 + SVM\n", "* AdaBoost.M1 + 决策树\n", "\n", "注意集成学习的核心算法需要**手动进行实现**，基分类器可以调库。\n", "\n", "### 基本要求\n", "* 根据数据格式设计特征的表示\n", "* 汇报不同组合下得到的 AUC\n", "* 结合不同集成学习算法的特点分析结果之间的差异\n", "* （使用 sklearn 等第三方库的集成学习算法会酌情扣分）\n", "\n", "### 扩展要求\n", "* 尝试其他基分类器（如 k-NN、朴素贝叶斯）\n", "* 分析不同特征的影响\n", "* 分析集成学习算法参数的影响"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 三、数据概览"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd \n", "train_df = pd.read_csv('./data/train.csv', sep='\\t')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>reviewerID</th>\n", "      <th>asin</th>\n", "      <th>reviewText</th>\n", "      <th>overall</th>\n", "      <th>votes_up</th>\n", "      <th>votes_all</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7885</td>\n", "      <td>3901</td>\n", "      <td>First off, allow me to correct a common mistak...</td>\n", "      <td>5.0</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>52087</td>\n", "      <td>47978</td>\n", "      <td>I am really troubled by this <PERSON> and <PERSON><PERSON><PERSON>...</td>\n", "      <td>3.0</td>\n", "      <td>99</td>\n", "      <td>134</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5701</td>\n", "      <td>3667</td>\n", "      <td>A near-perfect film version of a downright glo...</td>\n", "      <td>4.0</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>47191</td>\n", "      <td>40892</td>\n", "      <td>Keep your expectations low.  Really really low...</td>\n", "      <td>1.0</td>\n", "      <td>4</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>40957</td>\n", "      <td>15367</td>\n", "      <td>\"they dont make em like this no more...\"well.....</td>\n", "      <td>5.0</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57034</th>\n", "      <td>58315</td>\n", "      <td>29374</td>\n", "      <td>If you like beautifully shot, well acted films...</td>\n", "      <td>2.0</td>\n", "      <td>12</td>\n", "      <td>21</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57035</th>\n", "      <td>23328</td>\n", "      <td>45548</td>\n", "      <td>This is a great set of films <PERSON> did <PERSON> and...</td>\n", "      <td>5.0</td>\n", "      <td>15</td>\n", "      <td>18</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57036</th>\n", "      <td>27203</td>\n", "      <td>42453</td>\n", "      <td>It's what's known as a comedy of manners. It's...</td>\n", "      <td>3.0</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57037</th>\n", "      <td>33992</td>\n", "      <td>44891</td>\n", "      <td><PERSON> can do no wrong as far a creating wonder...</td>\n", "      <td>5.0</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57038</th>\n", "      <td>27478</td>\n", "      <td>19198</td>\n", "      <td>I agree with everyone else that this is a grea...</td>\n", "      <td>2.0</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>57039 rows × 7 columns</p>\n", "</div>"], "text/plain": ["       reviewerID   asin                                         reviewText  \\\n", "0            7885   3901  First off, allow me to correct a common mistak...   \n", "1           52087  47978  I am really troubled by this <PERSON> and <PERSON><PERSON><PERSON>...   \n", "2            5701   3667  A near-perfect film version of a downright glo...   \n", "3           47191  40892  Keep your expectations low.  Really really low...   \n", "4           40957  15367  \"they dont make em like this no more...\"well.....   \n", "...           ...    ...                                                ...   \n", "57034       58315  29374  If you like beautifully shot, well acted films...   \n", "57035       23328  45548  This is a great set of films <PERSON> did <PERSON> and...   \n", "57036       27203  42453  It's what's known as a comedy of manners. It's...   \n", "57037       33992  44891  <PERSON> can do no wrong as far a creating wonder...   \n", "57038       27478  19198  I agree with everyone else that this is a grea...   \n", "\n", "       overall  votes_up  votes_all  label  \n", "0          5.0         6          7      0  \n", "1          3.0        99        134      0  \n", "2          4.0        14         14      1  \n", "3          1.0         4          7      0  \n", "4          5.0         3          6      0  \n", "...        ...       ...        ...    ...  \n", "57034      2.0        12         21      0  \n", "57035      5.0        15         18      0  \n", "57036      3.0         4          5      0  \n", "57037      5.0         4          5      0  \n", "57038      2.0         5          5      1  \n", "\n", "[57039 rows x 7 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["train_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["本次数据来源于 Amazon 电商平台，包含超过 50,000 条用户在购买商品后留下的评论，各列的含义如下：\n", "\n", "* reviewerID：用户 ID\n", "* asin：商品 ID\n", "* reviewText：英文评论文本\n", "* overall：用户对商品的打分（1-5）\n", "* votes_up：认为评论有用的点赞数（只在训练集出现）\n", "* votes_all：该评论得到的总评价数（只在训练集出现）\n", "* label：评论质量的 label，1 表示高质量，0 表示低质量（只在训练集出现）\n", "\n", "评论质量的 label 来自于其他用户对评论的 votes，votes_up/votes_all ≥ 0.9 的作为高质量评论。此外测试集包含一个额外的列 ID，标识了每一个测试的样例。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 四、云平台提交格式\n", "\n", "提交文件需要对测试集中每一条评论给出预测为高质量的概率，每行包括一个 ID（和测试集对应）以及预测的概率 Prediction（0-1的浮点数），用逗号分隔。示例提交格式如下：\n", "\n", "```\n", "ID,Prediction\n", "0,0.9\n", "1,0.45\n", "2,0.78\n", "...\n", "```\n", "\n", "文件命名没有要求。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**注意除了提交云平台，还需要像之前作业一样在学堂在线提交代码和报告（不包括数据）**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "MachineLearning", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 4}